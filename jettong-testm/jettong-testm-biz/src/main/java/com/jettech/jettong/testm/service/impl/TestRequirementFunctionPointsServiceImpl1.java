package com.jettech.jettong.testm.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.fileimport.model.ImportProgressEvent;
import com.jettech.basic.fileimport.model.ImportResult;
import com.jettech.basic.fileimport.model.ImportStatus;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.alm.api.ProjectApi;
import com.jettech.jettong.alm.api.TaskApi;
import com.jettech.jettong.alm.api.TestreqApi;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.jettong.common.constant.FileProcessorConstants;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.common.util.poi.ExcelExportPlusUtil;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.config.HierarchyConfig;
import com.jettech.jettong.testm.dao.TestRequirementFunctionPointsMapper;
import com.jettech.jettong.testm.dto.FunctionPointsImportTaskSaveDTO;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsPageQuery;
import com.jettech.jettong.testm.entity.TestRequirementAnalysisConfig;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.poi.TestRequirementFunctionPointDicHandlerImpl;
import com.jettech.jettong.testm.poi.TestRequirementFunctionPointVerifyHandler;
import com.jettech.jettong.testm.service.TestRequirementAnalysisConfigService;
import com.jettech.jettong.testm.service.TestRequirementFunctionPointsService;
import com.jettech.jettong.testm.vo.PointsStyleVo;
import com.jettech.jettong.testm.vo.TestPointsTreeNodeVO;
import com.jettech.jettong.testm.vo.TestPointsTreeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.Serializable;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.poi.ss.usermodel.Font.COLOR_RED;

/**
 * 测试分析功能要点表业务层
 *
 * <AUTHOR>
 * @version 1.0
 * @description 测试分析功能要点表业务层，包含脑图生成功能
 * @projectName jettong
 * @package com.jettech.jettong.testm.service.impl
 * @className TestRequirementFunctionPointsServiceImpl
 * @date 2025-07-24
 * @copyright 2021 www.jettech.cn Inc. All rights reserved.
 * @ 注意：本内容仅限于捷科智诚北京有限公司内部传阅，禁止外泄以及用于其他的商业目的
 */
@Slf4j
@Service
@DS("#thread.tenant")
public class TestRequirementFunctionPointsServiceImpl1
        extends SuperServiceImpl<TestRequirementFunctionPointsMapper, TestRequirementFunctionPoints>
        implements TestRequirementFunctionPointsService
{
    private final ProductInfoApi productInfoApi;
    private final TestRequirementAnalysisConfigService configService;
    private final TestreqApi testreqApi;
    private final ProjectApi projectApi;
    private final ProductModuleFunctionApi productModuleFunctionApi;
    private final UserApi userApi;
    private final TaskApi taskApi;

    public TestRequirementFunctionPointsServiceImpl1(ProductInfoApi productInfoApi, TestRequirementAnalysisConfigService configService, TestreqApi testreqApi, ProjectApi projectApi, ProductModuleFunctionApi productModuleFunctionApi,
            UserApi userApi, TaskApi taskApi) {
        this.productInfoApi = productInfoApi;
        this.configService = configService;
        this.testreqApi = testreqApi;
        this.projectApi = projectApi;
        this.productModuleFunctionApi = productModuleFunctionApi;
        this.userApi = userApi;
        this.taskApi = taskApi;
    }

    public static final String SHEETNAME = "测试分析";

    @Override
    public List<Long> getFunctionIdsByProjectAndModule(Long projectId, Long productModuleFunctionId)
    {
        List<Long> byProjectId = productInfoApi.findByProjectId(projectId, productModuleFunctionId);
        return byProjectId;
    }

    @Override
    public List<Long> getFunctionIdsByFunctionId(Long productModuleFunctionId)
    {
        List<Long> byProjectId = productInfoApi.findByFunctionId(productModuleFunctionId);
        return byProjectId;
    }

    @Override
    public void addCaseCount(Long pointId)
    {
        TestRequirementFunctionPoints byId = this.getById(pointId);
        byId.setCaseCount(byId.getCaseCount() + 1);
        this.updateById(byId);
    }

    @Override
    public void redCaseCount(Long pointId)
    {
        TestRequirementFunctionPoints byId = this.getById(pointId);
        byId.setCaseCount(byId.getCaseCount() - 1);
        this.updateById(byId);
    }

    /**
     * 生成测试要点脑图树形结构
     *
     * @param projectId 项目ID，不能为空
     * @param reqId 需求ID，可以为空
     * @return TestPointsTreeVO 脑图对象
     * <AUTHOR>
     * @description 根据项目ID和需求ID生成脑图
     * @date 2025-08-25
     */
    @Override
    public TestPointsTreeVO generateTestPointsTree(Long projectId, Long reqId, Long taskId)
    {
        TestPointsTreeVO result = new TestPointsTreeVO();

        // 获取配置
        TestRequirementAnalysisConfig config = getEnabledConfig();
        if (config == null || config.getHierarchyConfig() == null || config.getFieldConfig() == null ||
                config.getTestpointParsingConfig() == null)
        {
            throw new BizException("未找到有效的测试需求分析配置");
        }

        // 解析配置
        HierarchyConfig hierarchyConfig = JSON.parseObject(config.getHierarchyConfig(), HierarchyConfig.class);

        // 直接构建简单的树形结构
        TestPointsTreeNodeVO rootNode = buildTreeStructure(projectId, reqId, taskId, hierarchyConfig);
        if (rootNode == null)
        {
            throw new BizException("脑图生成失败");
        }

        // 设置 layout
        String layout = hierarchyConfig.getLayout() != null ? hierarchyConfig.getLayout() : "logicalStructure";
        result.setLayout(layout);

        // 设置 root
        result.setRoot(rootNode);

        // 设置 theme
        JSONObject theme = buildThemeFromConfig(hierarchyConfig);
        result.setTheme(theme);

        return result;
    }

    /**
     * @param projectId
     * @param taskId
     * @return
     * @throws
     * <AUTHOR>
     * @date 2025/9/7 17:20
     * @update wzj 2025/9/7 17:20
     * @since 1.0
     */
    @Override
    public void template(Long projectId, Long taskId)
    {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getResponse();
        try
        {
            TestRequirementFunctionPointDicHandlerImpl dicHandler = new TestRequirementFunctionPointDicHandlerImpl();
            List<List<ProductModuleFunction>> cascadeData = initializeHandlers(dicHandler, null, projectId, taskId, null);

            Workbook workbook = buildExcelExportWorkbook(dicHandler, null, cascadeData);

            ExcelDownLoadUtil.export(response, workbook, "测试分析模板.xlsx");
        }
        catch (Exception e)
        {
            log.error("导出测试分析模板失败", e);
        }

    }


    /**
     * 设置字典及验证处理器并返回级联数据
     * @param dicHandler
     * @param verifyHandler
     * @param projectId
     * @param taskId
     * @param testReqId
     * @return {@link List< List< ProductModuleFunction>>}
     * @throws
     * <AUTHOR>
     * @date 2025/9/11 15:35
     * @update wzj 2025/9/11 15:35
     * @since 1.0
     */
    private List<List<ProductModuleFunction>> initializeHandlers(TestRequirementFunctionPointDicHandlerImpl dicHandler, TestRequirementFunctionPointVerifyHandler verifyHandler, Long projectId, Long taskId, Long testReqId) {
        // 设置字典处理器
        TestRequirementAnalysisConfig config = getEnabledConfig();
        dicHandler.setTestRequirementAnalysisConfig(config);

        List<ProductModuleFunction> moduleFunctions = productModuleFunctionApi.findModuleFunctionByProjectId(projectId, taskId, testReqId, null);
        List<Long> productIds = moduleFunctions.stream().map(ProductModuleFunction::getProductId).distinct().collect(Collectors.toList());
        List<ProductInfo> productInfos = productInfoApi.selectProductInfoByIds(productIds);
        dicHandler.setProductAndFunction(productInfos, moduleFunctions);

        // 如果校验处理器存在，则初始化
        if (verifyHandler != null) { 
            verifyHandler.init(moduleFunctions);
        }

        // 准备并返回级联数据
        List<ProductModuleFunction> tradeFunctions = moduleFunctions.stream().filter(mf -> mf.getNodeType() == 2).collect(Collectors.toList());
        tradeFunctions.forEach(tf -> tf.setParentId(tf.getProductId()));

        List<ProductModuleFunction> systemEntities = productInfos.stream().map(pi -> {
            ProductModuleFunction treeEntity = new ProductModuleFunction();
            treeEntity.setId(pi.getId());
            treeEntity.setName(pi.getName());
            treeEntity.setParentId(null);
            return treeEntity;
        }).collect(Collectors.toList());

        return Arrays.asList(systemEntities, tradeFunctions);
    }


    /**
     * @return
     * @throws
     * <AUTHOR>
     * @date 2025/9/7 17:20
     * @update wzj 2025/9/7 17:20
     * @since 1.0
     */
    @Override
    public void export(TestRequirementFunctionPointsPageQuery query, Boolean isWHL)
    {
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getResponse();
        try
        {
            TestRequirementFunctionPointDicHandlerImpl dicHandler = new TestRequirementFunctionPointDicHandlerImpl();
            List<List<ProductModuleFunction>> cascadeData = initializeHandlers(dicHandler, null, query.getProjectId(), query.getTaskId(), query.getIssueTestReqId());

            // 根据条件查询待导出数据
            List<Long> functionIds = new ArrayList<>();
            if(query.getFunctionAndModuleId()!=null && query.getFunctionAndModuleId()!=0L ){
                functionIds = this.getFunctionIdsByFunctionId(query.getFunctionAndModuleId());

                if(functionIds.isEmpty()){
                    // 如果没有匹配的功能ID，则导出空文件
                    Workbook workbook = buildExcelExportWorkbook(dicHandler, new ArrayList<>(), cascadeData);
                    ExcelDownLoadUtil.export(response, workbook, "测试分析数据.xlsx");
                    return;
                }
            }

            LbqWrapper<TestRequirementFunctionPoints> wrapper = Wraps.lbQ();
            wrapper.eq(query.getIssueTestReqId() != null, TestRequirementFunctionPoints::getIssueTestReqId, query.getIssueTestReqId())
                    .eq(query.getFunctionId() != null, TestRequirementFunctionPoints::getFunctionId, query.getFunctionId())
                    .eq(query.getProjectId() != null, TestRequirementFunctionPoints::getProjectId, query.getProjectId())
                    .eq(query.getStateCode() != null && !query.getStateCode().isEmpty(), TestRequirementFunctionPoints::getStateCode, query.getStateCode())
                    .eq(query.getTaskId() != null, TestRequirementFunctionPoints::getTaskId, query.getTaskId())
                    .like(query.getFunctionPoint() != null && !query.getFunctionPoint().isEmpty(), TestRequirementFunctionPoints::getFunctionPoint, query.getFunctionPoint())
                    .like(query.getTestPoints() != null && !query.getTestPoints().isEmpty(), TestRequirementFunctionPoints::getTestPoints, query.getTestPoints())
                    .eq(query.getRuleType() != null && !query.getRuleType().isEmpty(), TestRequirementFunctionPoints::getRuleType, query.getRuleType())
                    .eq(query.getInvolveAccount() != null, TestRequirementFunctionPoints::getInvolveAccount, query.getInvolveAccount())
                    .eq(query.getInvolveBatch() != null, TestRequirementFunctionPoints::getInvolveBatch, query.getInvolveBatch())
                    .eq(query.getPriority() != null && !query.getPriority().isEmpty(), TestRequirementFunctionPoints::getPriority, query.getPriority())
                    .in(!functionIds.isEmpty(), TestRequirementFunctionPoints::getFunctionId, functionIds);

            List<TestRequirementFunctionPoints> data = list(wrapper);

            List<ProductModuleFunction> functions = cascadeData.get(1);
            // 构建功能id 和 productId map
            Map<Long, Long> functionIdToProductId = functions.stream()
                    .collect(Collectors.toMap(ProductModuleFunction::getId, ProductModuleFunction::getProductId));

            for (TestRequirementFunctionPoints datum : data)
            {
                datum.setProductId(functionIdToProductId.get(datum.getFunctionId()));
            }

            Workbook workbook = buildExcelExportWorkbook(dicHandler, data, cascadeData);

            ExcelDownLoadUtil.export(response, workbook, "测试分析数据.xlsx");
        }
        catch (Exception e)
        {
            log.error("导出测试分析数据失败", e);
            throw new BizException("导出测试分析数据失败: " + e.getMessage());
        }
    }


    private Workbook buildExcelExportWorkbook(TestRequirementFunctionPointDicHandlerImpl dicHandler,
            List<TestRequirementFunctionPoints> data, List<List<ProductModuleFunction>> sysAndTradeData)
    {
        ExportParams exportParams = new ExportParams();
        if (data != null && !data.isEmpty()) {
            Set<Serializable> userIds = data.stream().map(TestRequirementFunctionPoints::getCreatedBy).collect(Collectors.toSet());
            Map<Serializable, Object> userMap = userApi.findByIds(userIds);
            data.forEach(item -> {
                Object userObj = userMap.get(String.valueOf(item.getCreatedBy()));
                if (userObj != null) {
                    User user = BeanPlusUtil.toBean(userObj, User.class);
                    item.setHandler(user.getName());
                }
            });
        }else {
            String[] exclusion = new String[]{ "handler", "caseCount"}; //导入模板不需要这两个字段
            exportParams.setExclusions(exclusion);
        }



        exportParams.setSheetName(SHEETNAME);
        exportParams.setDictHandler(dicHandler);
        exportParams.setType(ExcelType.XSSF);
        exportParams.setTitle("1、导入模板不同任务不能通用\n2、带*的列必须填写\n3、如果测试点名称在当前任务下已存在，则更新现有数据；否则，创建新数据。");
        if (data == null)
        {
            data = new ArrayList<>();
        }
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, TestRequirementFunctionPoints.class, data);
        Row row = workbook.getSheetAt(0).getRow(0);
        // 设置title单元格行高
        row.setHeightInPoints(47);
        CellStyle cellStyle = row.getCell(0).getCellStyle();
        // 设置title单元格\n强制换行
        cellStyle.setWrapText(true);
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(COLOR_RED);
        cellStyle.setFont(font);
        // 设置title单元格居左
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        // 设置级联
        ExcelExportPlusUtil.createMultiLevelCascade(workbook, SHEETNAME, sysAndTradeData,
                new ExcelExportPlusUtil.CascadeConfig(0, 2, 9999));
        return workbook;
    }


    /**
     * 构建简单的树形结构
     *
     * @param projectId 项目ID
     * @param reqId 任务 ID
     * @param hierarchyConfig 层级配置
     * @return TestPointsTreeNodeVO 树形根节点
     * <AUTHOR>
     * @description 根据配置构建树结构
     * @date 2025-08-25
     */
    private TestPointsTreeNodeVO buildTreeStructure(Long projectId, Long reqId, Long taskId,
            HierarchyConfig hierarchyConfig)
    {


        // 根据项目编号（必要）需求编号（可选）或任务编号（可选）三个条件过滤查询测试点

        LbqWrapper<TestRequirementFunctionPoints> wrapper = 
                Wraps.<TestRequirementFunctionPoints>lbQ().eq(TestRequirementFunctionPoints::getProjectId, projectId);
        if (taskId != null)
        {
            wrapper.eq(TestRequirementFunctionPoints::getTaskId, taskId);
        }
        if (reqId != null)
        {
            wrapper.eq(TestRequirementFunctionPoints::getIssueTestReqId, reqId);
        }

        List<TestRequirementFunctionPoints> functionPoints = list(wrapper);

        // 提取 functionpoint 的 functionid
        List<Long> functionIds = functionPoints.stream().map(TestRequirementFunctionPoints::getFunctionId).distinct()
                .collect(Collectors.toList());

        // 根据 functionIds 获取 交易
        List<ProductModuleFunction> functions = productModuleFunctionApi.getProductModuleFunctionListByIds(functionIds);

        if (functions.isEmpty())
        {
            return null;
        }

        // 获取关联系统
        List<Long> productIds =
                functions.stream().map(ProductModuleFunction::getProductId).distinct().collect(Collectors.toList());
        List<ProductInfo> productInfos = productInfoApi.selectProductInfoByIds(productIds);

        Map<Long, String> productMap =
                productInfos.stream().collect(Collectors.toMap(ProductInfo::getId, ProductInfo::getName));

        // 获取启用的层级配置，按level排序
        List<HierarchyConfig.LevelConfig> enabledLevels =
                hierarchyConfig.getHierarchy().stream().filter(HierarchyConfig.LevelConfig::getEnabled)
                        .sorted(Comparator.comparingInt(HierarchyConfig.LevelConfig::getLevel))
                        .collect(Collectors.toList());


        // 创建根节点
        TestPointsTreeNodeVO rootNode = createRootTreeNodeFromConfig(projectId, reqId, hierarchyConfig);


        // 根据配置构建完整的层级结构
        long nodeId = 1L;

        // 按系统分组功能
        Map<Long, List<ProductModuleFunction>> functionsByProduct =
                functions.stream().collect(Collectors.groupingBy(ProductModuleFunction::getProductId));

        for (Map.Entry<Long, List<ProductModuleFunction>> entry : functionsByProduct.entrySet())
        {
            Long productId = entry.getKey();
            List<ProductModuleFunction> productFunctions = entry.getValue();
            // 获取第二级的节点配置
            HierarchyConfig.LevelConfig systemConfig = getLevelConfigByLevelAndType(enabledLevels, 2, "system");
            // 创建系统节点 (Level 2)
            String systemName = productMap.get(productId);
            String systemNodeName =
                    buildNodeNameFromTemplate(systemConfig.getNameTemplate(), projectId, reqId, systemName, null, null);
            TestPointsTreeNodeVO systemNode =
                    createTreeNodeVO(systemNodeName, nodeId++, rootNode.getId(), systemConfig.getStyle());
            // 获取交易的节点配置
            HierarchyConfig.LevelConfig tradeConfig = getLevelConfigByLevelAndType(enabledLevels, 3, "trade");
            // 按交易分组（nodeType=2 表示交易）
            List<ProductModuleFunction> trades =
                    productFunctions.stream().filter(f -> f.getNodeType() != null && f.getNodeType() == 2)
                            .collect(Collectors.toList());

            for (ProductModuleFunction trade : trades)
            {
                // 根据配置模板创建交易节点
                String tradeNodeName =
                        buildNodeNameFromTemplate(tradeConfig.getNameTemplate(), projectId, reqId, null, trade, null);
                TestPointsTreeNodeVO tradeNode =
                        createTreeNodeVO(tradeNodeName, nodeId++, systemNode.getId(), tradeConfig.getStyle());

                // 查找属于当前交易的测试点
                List<TestRequirementFunctionPoints> tradeFunctionPoints =
                        functionPoints.stream().filter(point -> trade.getId().equals(point.getFunctionId()))
                                .collect(Collectors.toList());

                // 为每个测试点创建节点 (Level 4)
                HierarchyConfig.LevelConfig functionPointConfig =
                        getLevelConfigByLevelAndType(enabledLevels, 4, "functionPoint");
                for (TestRequirementFunctionPoints functionPoint : tradeFunctionPoints)
                {
                    String pointName = buildNodeNameFromTemplate(functionPointConfig.getNameTemplate(), null,
                            null, null, null, functionPoint);
                    TestPointsTreeNodeVO testPointNode =
                            createTreeNodeVO(pointName, nodeId++, tradeNode.getId(),
                                    functionPointConfig.getStyle());

                    // 为测试点构建属性层级 (Level 5)
                    nodeId = buildAttributeNodesForTestPoint(testPointNode, functionPoint, enabledLevels, nodeId);

                    tradeNode.getChildren().add(testPointNode);
                }

                // 只有当交易有测试点时才添加到系统节点
                if (!tradeNode.getChildren().isEmpty())
                {
                    systemNode.getChildren().add(tradeNode);
                }
            }

            // 只有当系统有交易时才添加到根节点
            if (!systemNode.getChildren().isEmpty())
            {
                rootNode.getChildren().add(systemNode);
            }
        }

        return rootNode;
    }

    /**
     * 根据传入的级别和类型获取节点配置，如果类型没传入则只判断级别，如果类型有传入则还需判断类型
     *
     * @param enabledLevels 启用的层级配置列表
     * @param level 层级编号
     * @param type 层级类型
     * @return {@link null}
     * @throws
     * <AUTHOR>
     * @date 2025/8/27 14:05
     * @update wzj 2025/8/27 14:05
     * @since 1.0
     */
    private HierarchyConfig.LevelConfig getLevelConfigByLevelAndType(List<HierarchyConfig.LevelConfig> enabledLevels,
            int level, String type)
    {
        return enabledLevels.stream().filter(levelConfig -> levelConfig.getLevel() == level &&
                        (type == null || type.equals(levelConfig.getType())) && levelConfig.getEnabled()).findFirst()
                .orElse(null);
    }


    /**
     * 为测试点构建属性节点
     *
     * @param testPointNode 测试点节点
     * @param testPoint 测试点数据
     * @param enabledLevels 启用的层级配置列表
     * @param nodeId 当前节点ID
     * @return long 更新后的节点ID
     * <AUTHOR>
     * @description 根据配置为测试点构建属性层级节点，对测试要素分割以及多选字段等特殊处理
     * @date 2025-08-25
     */
    private long buildAttributeNodesForTestPoint(TestPointsTreeNodeVO testPointNode,
            TestRequirementFunctionPoints testPoint, List<HierarchyConfig.LevelConfig> enabledLevels, long nodeId)
    {

        // 获取属性层级的所有配置
        List<HierarchyConfig.LevelConfig> attributeConfigs =
                enabledLevels.stream().filter(level -> "attribute".equals(level.getType()))
                        .sorted(Comparator.comparingInt(HierarchyConfig.LevelConfig::getSort))
                        .collect(Collectors.toList());

        // 获取当前启用的配置，用于获取测试要素分割配置
        TestRequirementAnalysisConfig config = getEnabledConfig();

        for (HierarchyConfig.LevelConfig attributeConfig : attributeConfigs)
        {
            String nameTemplate = attributeConfig.getNameTemplate();
            String dataField = attributeConfig.getDataField();

            TestPointsTreeNodeVO attributeNode =
                    createTreeNodeVO(nameTemplate, nodeId++, testPointNode.getId(), attributeConfig.getStyle());

            // 根据数据字段获取对应的值
            Object attributeValue = getAttributeValue(testPoint, dataField, config);

            // 处理不同类型的属性值
            nodeId = addAttributeValueNodes(attributeNode, attributeValue, dataField, testPoint, config, nodeId);

            testPointNode.getChildren().add(attributeNode);
        }

        return nodeId;
    }

    /**
     * 添加属性值节点
     *
     * @param attributeNode 属性节点
     * @param attributeValue 属性值（可能是String或List<String>）
     * @param dataField 数据字段名
     * @param testPoint 测试点数据
     * @param config 配置
     * @param nodeId 当前节点ID
     * @return long 更新后的节点ID
     */
    private long addAttributeValueNodes(TestPointsTreeNodeVO attributeNode, Object attributeValue, String dataField, 
            TestRequirementFunctionPoints testPoint, TestRequirementAnalysisConfig config, long nodeId)
    {

        // 对于测试要素，需要特殊处理（分割）
        if ("testPoints".equals(dataField))
        {
            List<String> testElements = parseTestPoints(testPoint.getTestPoints(), config);
            if (!testElements.isEmpty())
            {
                for (String element : testElements)
                {
                    TestPointsTreeNodeVO elementNode = createTreeNodeVO(element, nodeId++, attributeNode.getId(), null);
                    attributeNode.getChildren().add(elementNode);
                }
            }
            else
            {
                TestPointsTreeNodeVO emptyNode = createTreeNodeVO("未设置", nodeId++, attributeNode.getId(), null);
                attributeNode.getChildren().add(emptyNode);
            }
        }
        else if (attributeValue instanceof JSONArray)
        {
            // 处理 MULTI-SELECT 类型返回的选项数组
            JSONArray options = (JSONArray) attributeValue;

            for (int i = 0; i < options.size(); i++)
            {
                JSONObject option = options.getJSONObject(i);
                String text = option.getString("text");
                JSONObject style = option.getJSONObject("style");

                TestPointsTreeNodeVO valueNode = createTreeNodeVO(
                        text,
                        nodeId++,
                        attributeNode.getId(),
                        style
                );
                attributeNode.getChildren().add(valueNode);
            }
        }
        else if (attributeValue instanceof JSONObject)
        {
            // 处理 SELECT 类型返回的单个选项对象
            JSONObject option = (JSONObject) attributeValue;
            String text = option.getString("text");
            JSONObject style = option.getJSONObject("style");

            TestPointsTreeNodeVO valueNode = createTreeNodeVO(
                    text,
                    nodeId++,
                    attributeNode.getId(),
                    style
            );
            attributeNode.getChildren().add(valueNode);
        }
        else
        {
            // 处理单个值（TEXT、TEXTAREA 类型）
            String value = attributeValue != null ? attributeValue.toString() : "未设置";
            TestPointsTreeNodeVO valueNode = createTreeNodeVO(value, nodeId++, attributeNode.getId(), null);
            attributeNode.getChildren().add(valueNode);
        }

        return nodeId;
    }


    /**
     * 获取启用的配置
     *
     * @return 启用的测试需求分析配置，如果没有启用的配置则返回null
     * <AUTHOR>
     * @description 查询数据库中enable=true的配置记录，如果有多个启用配置则返回第一个
     * @date 2025-08-25
     */
    private TestRequirementAnalysisConfig getEnabledConfig()
    {
        List<TestRequirementAnalysisConfig> configs = configService.list(
                Wraps.<TestRequirementAnalysisConfig>lbQ().eq(TestRequirementAnalysisConfig::getEnable, true));
        return configs.isEmpty() ? null : configs.get(0);
    }


    /**
     * 根据配置创建树形根节点
     *
     * @param projectId 项目ID
     * @param reqId 需求ID
     * @param hierarchyConfig 层级配置
     * @return 根节点
     * <AUTHOR>
     * @date 2025-08-25
     */
    private TestPointsTreeNodeVO createRootTreeNodeFromConfig(Long projectId, Long reqId, 
            HierarchyConfig hierarchyConfig)
    {
        // 查找根节点配置
        HierarchyConfig.LevelConfig rootConfig = getLevelConfigByLevelAndType(hierarchyConfig.getHierarchy(), 1, null);

        if (rootConfig == null && rootConfig.getNameTemplate() == null)
        {
            throw new BizException("测试需求分析配置有误");
        }

        // 使用配置的模板创建根节点
        String rootName = buildNodeNameFromTemplate(rootConfig.getNameTemplate(), projectId, reqId, null, null, null);
        return createTreeNodeVO(rootName, 0L, null, rootConfig.getStyle());

    }

    /**
     * 创建节点
     *
     * @param name 节点名称
     * @param id 节点ID
     * @param parentId 父节点ID
     * @return TestPointsTreeNodeVO 节点
     */
    private TestPointsTreeNodeVO createTreeNodeVO(String name, Long id, Long parentId, JSONObject style)
    {
        TestPointsTreeNodeVO node = new TestPointsTreeNodeVO();
        node.setId(id);
        node.setParentId(parentId);
        node.setName(name);
        node.setSort(0);

        // 设置节点数据
        PointsStyleVo styleVo = new PointsStyleVo();
        styleVo.setText(name);

        node.setData(styleVo);

        // 初始化子节点列表
        node.initChildren();

        // 将 style 中定义的样式属性 copy 至node对象中
        if (style != null)
        {
            styleVo.setFillColor(style.getString("fillColor"));
            styleVo.setBorderColor(style.getString("borderColor"));
            styleVo.setBorderRadius(style.getInteger("borderRadius"));
            styleVo.setBorderWidth(style.getInteger("borderWidth"));
            styleVo.setColor(style.getString("color"));
            styleVo.setLineColor(style.getString("lineColor"));
        }

        return node;
    }


    /**
     * 根据模板构建节点名称
     *
     * @param nameTemplate 名称模板
     * @param projectId 项目ID
     * @param reqId 需求ID
     * @param function 功能对象
     * @param point 测试点对象
     * @return String 构建后的节点名称
     * <AUTHOR>
     * @date 2025-08-25
     */
    private String buildNodeNameFromTemplate(String nameTemplate, Long projectId, Long reqId, String systemName,
            ProductModuleFunction function, TestRequirementFunctionPoints point)
    {
        if (nameTemplate == null)
        {
            return "未命名节点";
        }

        String result = nameTemplate;

        // 替换项目相关占位符
        if (projectId != null)
        {
            String projectName = getProjectName(projectId);
            result = result.replace("{projectName}", projectName);
        }

        // 替换需求相关占位符
        if (reqId != null)
        {
            String reqName = getRequirementName(reqId);
            result = result.replace("{reqName}", reqName);
        }


        if (systemName != null)
        {
            result = result.replace("{systemName}", systemName);
        }

        // 替换功能相关占位符
        if (function != null)
        {
            result = result.replace("{tradeName}", function.getName() != null ? function.getName() : "");
        }

        // 替换测试点相关占位符
        if (point != null)
        {
            result =
                    result.replace("{functionPoint}", point.getFunctionPoint() != null ? point.getFunctionPoint() : "");
        }

        return result;
    }

    /**
     * 获取项目名称
     *
     * @param projectId 项目ID
     * @return String 项目名称
     * <AUTHOR>
     * @date 2025-08-25
     */
    private String getProjectName(Long projectId)
    {
        try
        {

            ProjectInfo project = projectApi.findById(projectId);
            return project != null ? project.getName() : "项目";
        }
        catch (Exception e)
        {
            log.warn("获取项目名称失败: {}", e.getMessage());
            return "项目";
        }
    }

    /**
     * 获取需求名称
     *
     * @param reqId 需求ID
     * @return String 需求名称
     * <AUTHOR>
     * @date 2025-08-25
     */
    private String getRequirementName(Long reqId)
    {
        try
        {
            Testreq testreq = testreqApi.getTestreq(reqId);
            return testreq != null ? testreq.getName() : "需求";
        }
        catch (Exception e)
        {
            log.warn("获取需求名称失败: {}", e.getMessage());
            return "需求";
        }
    }


    /**
     * 根据数据字段获取属性值
     *
     * @param point 测试要点数据对象
     * @param dataField 数据字段名称
     * @param config 测试需求分析配置
     * @return String 属性值的字符串表示，未设置时返回"未设置"
     * <AUTHOR>
     * @description 根据数据字段名称从测试要点对象中获取对应的属性值，
     * @date 2025-08-25
     */
    private Object getAttributeValue(TestRequirementFunctionPoints point, String dataField,
            TestRequirementAnalysisConfig config)
    {
        try
        {
            // 从配置中获取字段配置
            JSONArray fieldConfigs = JSON.parseArray(config.getFieldConfig());
            JSONObject fieldConfig = findFieldConfig(fieldConfigs, dataField);

            if (fieldConfig == null)
            {
                throw new BizException("测试需求分析配置有误，未找到字段配置");
            }

            // 获取字段的原始值
            Object rawValue = getFieldValue(point, dataField);

            // 根据字段配置解析值
            return parseValueByFieldConfig(rawValue, fieldConfig);

        }
        catch (Exception e)
        {
            log.warn("解析字段值失败，dataField: {}, error: {}", dataField, e.getMessage());
            return getFieldValue(point, dataField);
        }
    }


    /**
     * 查找指定字段的配置
     *
     * @param fieldConfigs 字段配置数组
     * @param dataField 字段名
     * @return JSONObject 字段配置对象
     */
    private JSONObject findFieldConfig(JSONArray fieldConfigs, String dataField)
    {
        for (int i = 0; i < fieldConfigs.size(); i++)
        {
            JSONObject fieldConfig = fieldConfigs.getJSONObject(i);
            if (dataField.equals(fieldConfig.getString("key")))
            {
                return fieldConfig;
            }
        }
        return null;
    }

    /**
     * 获取字段的原始值
     *
     * @param point 测试要点对象
     * @param dataField 字段名
     * @return Object 字段值
     */
    private Object getFieldValue(TestRequirementFunctionPoints point, String dataField)
    {
        switch (dataField)
        {
            case "functionPoint":
                return point.getFunctionPoint();
            case "priority":
                return point.getPriority();
            case "ruleType":
                return point.getRuleType();
            case "involveAccount":
                return point.getInvolveAccount();
            case "involveBatch":
                return point.getInvolveBatch();
            case "testPoints":
                return point.getTestPoints();
            case "ruleSource":
                return point.getRuleSource();
            case "caseCount":
                return point.getCaseCount() == null ? 0 : point.getCaseCount();
            case "remark":
                return point.getRemark();
            case "c1":
                return point.getC1();
            case "c2":
                return point.getC2();
            case "c3":
                return point.getC3();
            case "c4":
                return point.getC4();
            case "c5":
                return point.getC5();
            default:
                return null;
        }
    }

    /**
     * 根据字段配置解析值
     *
     * @param rawValue 原始值
     * @param fieldConfig 字段配置
     * @return Object 解析后的值，可能是String或List<String>
     */
    private Object parseValueByFieldConfig(Object rawValue, JSONObject fieldConfig)
    {
        if (rawValue == null)
        {
            return "";
        }

        String fieldType = fieldConfig.getString("field_type");

        switch (fieldType)
        {
            case "SELECT":
                return parseSelectOption(rawValue, fieldConfig);
            case "MULTI-SELECT":
                return parseMultiSelectOptions(rawValue, fieldConfig);
            case "TEXT":
            case "TEXTAREA":
                return rawValue.toString();
            default:
                return rawValue.toString();
        }
    }

    /**
     * 解析单选类型字段的选项
     *
     * @param rawValue 原始值
     * @param fieldConfig 字段配置
     * @return JSONObject 匹配的选项对象，包含 text、value、style 等信息
     */
    private JSONObject parseSelectOption(Object rawValue, JSONObject fieldConfig)
    {
        JSONArray options = fieldConfig.getJSONArray("options");
        if (options == null || options.isEmpty())
        {
            // 返回一个基本的选项对象
            JSONObject fallbackOption = new JSONObject();
            fallbackOption.put("text", rawValue.toString());
            fallbackOption.put("value", rawValue);
            return fallbackOption;
        }

        // 查找匹配的选项
        for (int i = 0; i < options.size(); i++)
        {
            JSONObject option = options.getJSONObject(i);
            Object optionValue = option.get("value");

            // 处理不同类型的值比较
            if (isValueMatch(rawValue, optionValue))
            {
                return option; // 直接返回匹配的选项对象
            }
        }

        // 如果没有找到匹配的选项，返回基本选项对象
        JSONObject fallbackOption = new JSONObject();
        fallbackOption.put("text", rawValue.toString());
        fallbackOption.put("value", rawValue);
        return fallbackOption;
    }

    /**
     * 解析多选类型字段的选项
     *
     * @param rawValue 原始值（逗号分隔的字符串）
     * @param fieldConfig 字段配置
     * @return JSONArray 匹配的选项对象数组，每个元素包含 text、value、style 等信息
     */
    private JSONArray parseMultiSelectOptions(Object rawValue, JSONObject fieldConfig)
    {
        JSONArray result = new JSONArray();

        if (rawValue == null || rawValue.toString().trim().isEmpty())
        {
            JSONObject fallbackOption = new JSONObject();
            fallbackOption.put("text", "未设置");
            fallbackOption.put("value", "");
            result.add(fallbackOption);
            return result;
        }

        JSONArray options = fieldConfig.getJSONArray("options");
        if (options == null || options.isEmpty())
        {
            JSONObject fallbackOption = new JSONObject();
            fallbackOption.put("text", rawValue.toString());
            fallbackOption.put("value", rawValue);
            result.add(fallbackOption);
            return result;
        }

        // 分割逗号分隔的值
        String[] values = rawValue.toString().split(",");

        for (String value : values)
        {
            String trimmedValue = value.trim();
            if (trimmedValue.isEmpty())
            {
                continue;
            }

            // 查找匹配的选项
            boolean found = false;
            for (int i = 0; i < options.size(); i++)
            {
                JSONObject option = options.getJSONObject(i);
                Object optionValue = option.get("value");

                if (isValueMatch(trimmedValue, optionValue))
                {
                    result.add(option);
                    found = true;
                    break;
                }
            }

            // 如果没有找到匹配的选项，添加基本选项对象
            if (!found)
            {
                JSONObject fallbackOption = new JSONObject();
                fallbackOption.put("text", trimmedValue);
                fallbackOption.put("value", trimmedValue);
                result.add(fallbackOption);
            }
        }

        if (result.isEmpty())
        {
            JSONObject fallbackOption = new JSONObject();
            fallbackOption.put("text", "未设置");
            fallbackOption.put("value", "");
            result.add(fallbackOption);
        }

        return result;
    }


    /**
     * 判断值是否匹配
     *
     * @param rawValue 原始值
     * @param optionValue 选项值
     * @return boolean 是否匹配
     */
    private boolean isValueMatch(Object rawValue, Object optionValue)
    {
        if (rawValue == null && optionValue == null)
        {
            return true;
        }
        if (rawValue == null || optionValue == null)
        {
            return false;
        }

        // 字符串比较
        if (rawValue instanceof String && optionValue instanceof String)
        {
            return rawValue.equals(optionValue);
        }

        // 布尔值比较
        if (rawValue instanceof Boolean && optionValue instanceof Boolean)
        {
            return rawValue.equals(optionValue);
        }

        // 数值比较
        if (rawValue instanceof Number && optionValue instanceof Number)
        {
            return rawValue.toString().equals(optionValue.toString());
        }

        // 默认字符比较
        return rawValue.toString().equals(optionValue.toString());
    }


    /**
     * 根据配置解析测试要素
     *
     * @param testPoints 测试要素字符串
     * @param config 测试需求分析配置
     * @return List<String> 分割后的测试要素列表
     * <AUTHOR>
     * @description 根据配置分割测试要素字符串
     * @date 2025-08-25
     */
    private List<String> parseTestPoints(String testPoints, TestRequirementAnalysisConfig config)
    {
        List<String> elements = new ArrayList<>();

        if (testPoints == null || testPoints.trim().isEmpty())
        {
            return elements;
        }

        // 获取分割配置
        String parsingConfig = config.getTestpointParsingConfig();

        // 根据配置进行分割
        String[] splitElements = splitTestPointsByConfig(testPoints, parsingConfig);

        for (String element : splitElements)
        {
            String trimmed = element.trim();
            if (!trimmed.isEmpty())
            {
                elements.add(trimmed);
            }
        }

        return elements;
    }


    /**
     * 根据配置分割测试要素
     *
     * @param testPoints 待分割的测试要素字符串
     * @param parsingConfig 分割配置JSON字符串
     * @return String[] 分割后的字符串数组
     * <AUTHOR>
     * @description 根据配置中的分隔符和处理规则分割测试要素字符串
     * @date 2025-08-25
     */
    private String[] splitTestPointsByConfig(String testPoints, String parsingConfig)
    {
        try
        {
            JSONObject config = JSON.parseObject(parsingConfig);

            // 获取分隔符配置
            String[] separators = getSeparatorsFromConfig(config);

            // 构建正则表达式
            StringBuilder regexBuilder = new StringBuilder();
            regexBuilder.append("[");
            for (String separator : separators)
            {
                // 转义特殊字符
                regexBuilder.append(separator.replaceAll("([\\[\\]\\(\\)\\{\\}\\*\\+\\?\\^\\$\\|\\\\])", "\\\\$1"));
            }
            regexBuilder.append("]");

            // 分割字符串
            String[] elements = testPoints.split(regexBuilder.toString());

            // 根据配置处理结果
            return processElementsByConfig(elements, config);

        }
        catch (Exception e)
        {
            log.warn("解析测试要素分割配置失败，使用默认分割: {}", e.getMessage());
            // 使用默认分割方式
            return testPoints.split("[;；\n|]");
        }
    }

    /**
     * 从配置中获取分隔符
     *
     * @param config 配置JSON对象
     * @return String[] 分隔符数组
     * <AUTHOR>
     * @date 2025-08-25
     */
    private String[] getSeparatorsFromConfig(JSONObject config)
    {
        try
        {
            return (String[]) config.getJSONArray("separators").toArray(new Object[0]);
        }
        catch (Exception e)
        {
            // 默认分隔符
            return new String[]{ ";", "；", "\n", "|" };
        }
    }


    /**
     * 根据配置处理分割后的元素
     *
     * @param elements 分割后的元素数组
     * @param config 处理配置
     * @return String[] 处理后的元素数组
     * <AUTHOR>
     * @date 2025-08-25
     */
    private String[] processElementsByConfig(String[] elements, JSONObject config)
    {
        List<String> processedElements = new ArrayList<>();

        boolean trimWhitespace = config.getBooleanValue("trimWhitespace");
        boolean removeEmpty = config.getBooleanValue("removeEmpty");

        for (String element : elements)
        {
            String processed = element;

            if (trimWhitespace)
            {
                processed = processed.trim();
            }

            if (removeEmpty && processed.isEmpty())
            {
                continue;
            }

            processedElements.add(processed);
        }

        return processedElements.toArray(new String[0]);
    }


    /**
     * 根据配置构建主题JSON
     *
     * @param hierarchyConfig 层级配置
     * @return JSONObject 主题配置JSON对象
     * <AUTHOR>
     * @description 根据层级配置中的主题设置构建mindmap的主题配置，直接使用JSONObject
     * @date 2025-08-25
     */
    private JSONObject buildThemeFromConfig(HierarchyConfig hierarchyConfig)
    {
        // 获取主题配置，如果没有配置则使用默认值
        JSONObject themeConfig = hierarchyConfig.getTheme();
        if (themeConfig == null)
        {
            // 使用默认主题配置
            JSONObject defaultTheme = new JSONObject();
            defaultTheme.put("template", "classic11");
            JSONObject config = new JSONObject();
            config.put("lineStyle", "curve");
            config.put("rootLineKeepSameInCurve", true);
            config.put("rootLineStartPositionKeepSameInCurve", false);
            config.put("showLineMarker", false);
            config.put("nodeUseLineStyle", false);
            defaultTheme.put("config", config);
            return defaultTheme;
        }

        // 直接返回配置中的主题JSON对象
        return themeConfig;
    }


    @Override
    public ImportResult processFile(MultipartFile multipartFile, String s, Consumer<ImportProgressEvent> consumer, FunctionPointsImportTaskSaveDTO taskSaveDTO)
    {
        long startTime = System.currentTimeMillis();
        consumer.accept(ImportProgressEvent.builder().taskId(s).status(ImportStatus.PROCESSING).progress(0).message("开始处理文件...").build());

        TestRequirementFunctionPointDicHandlerImpl dicHandler = new TestRequirementFunctionPointDicHandlerImpl();
        TestRequirementFunctionPointVerifyHandler verifyHandler = new TestRequirementFunctionPointVerifyHandler();

        try (InputStream inputStream = multipartFile.getInputStream())
        {
            // 初始化处理器
            initializeHandlers(dicHandler, verifyHandler, taskSaveDTO.getProjectId(), taskSaveDTO.getTaskId(), taskSaveDTO.getTestReqId());

            // 设置导入参数
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setHeadRows(1);
            params.setNeedVerify(true);
            params.setVerifyHandler(verifyHandler);
            params.setDictHandler(dicHandler);

            consumer.accept(ImportProgressEvent.builder().taskId(s).status(ImportStatus.PROCESSING).progress(10).message("文件参数设置完成，准备解析...").build());

            // 解析Excel
            ExcelImportResult<TestRequirementFunctionPoints> result = ExcelImportUtil.importExcelMore(inputStream, TestRequirementFunctionPoints.class, params);

            consumer.accept(ImportProgressEvent.builder().taskId(s).status(ImportStatus.PROCESSING).progress(50).message("文件解析完成，开始处理数据...").build());

            List<TestRequirementFunctionPoints> successList = result.getList();
            List<TestRequirementFunctionPoints> failList = result.getFailList();

            List<TestRequirementFunctionPoints> pointsToInsert = new ArrayList<>();
            List<TestRequirementFunctionPoints> pointsToUpdate = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();
            Map<String, TestRequirementFunctionPoints> duplicates = new HashMap<>();

            if (!successList.isEmpty()) {
                LbqWrapper<TestRequirementFunctionPoints> queryWrapper = Wraps.<TestRequirementFunctionPoints>lbQ()
                        .eq(TestRequirementFunctionPoints::getProjectId, taskSaveDTO.getProjectId());
                if (taskSaveDTO.getTestReqId() != null) {
                    queryWrapper.eq(TestRequirementFunctionPoints::getIssueTestReqId, taskSaveDTO.getTestReqId());
                } else {
                    queryWrapper.isNull(TestRequirementFunctionPoints::getIssueTestReqId);
                }
                Map<String, TestRequirementFunctionPoints> dbPointsMap = this.list(queryWrapper)
                        .stream().collect(Collectors.toMap(TestRequirementFunctionPoints::getFunctionPoint, Function.identity(), (k1, k2) -> k1));

                Map<String, TestRequirementFunctionPoints> taskPointsMap = this.list(Wraps.<TestRequirementFunctionPoints>lbQ().eq(TestRequirementFunctionPoints::getTaskId, taskSaveDTO.getTaskId()))
                        .stream().collect(Collectors.toMap(TestRequirementFunctionPoints::getFunctionPoint, Function.identity(), (k1, k2) -> k1));


                for (TestRequirementFunctionPoints importedPoint : successList) {
                    importedPoint.setProjectId(taskSaveDTO.getProjectId());
                    importedPoint.setTaskId(taskSaveDTO.getTaskId());
                    importedPoint.setIssueTestReqId(taskSaveDTO.getTestReqId());

                    TestRequirementFunctionPoints taskExistingPoint = taskPointsMap.get(importedPoint.getFunctionPoint());
                    if (taskExistingPoint != null) {
                        importedPoint.setId(taskExistingPoint.getId());
                        pointsToUpdate.add(importedPoint);
                    } else {
                        TestRequirementFunctionPoints scopeExistingPoint = dbPointsMap.get(importedPoint.getFunctionPoint());
                        if (scopeExistingPoint != null) {
                            duplicates.put(importedPoint.getFunctionPoint(), scopeExistingPoint);
                        } else {
                            pointsToInsert.add(importedPoint);
                        }
                    }
                }
            }

            if (!duplicates.isEmpty()) {
                Set<Serializable> taskIds = duplicates.values().stream()
                        .map(TestRequirementFunctionPoints::getTaskId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());
                Map<Serializable, Object> taskResults = taskApi.findByIds(taskIds);
                Map<Long, Task> taskMap = new HashMap<>();
                if (taskResults != null) {
                    for (Map.Entry<Serializable, Object> entry : taskResults.entrySet()) {
                        taskMap.put(Long.valueOf(entry.getKey().toString()), BeanPlusUtil.toBean(entry.getValue(), Task.class));
                    }
                }

                for (Map.Entry<String, TestRequirementFunctionPoints> entry : duplicates.entrySet()) {
                    String functionPointName = entry.getKey();
                    TestRequirementFunctionPoints conflictingPoint = entry.getValue();
                    Task task = taskMap.get(conflictingPoint.getTaskId());
                    String taskName = (task != null) ? task.getName() : "未知";
                    errorMessages.add("功能点 '" + functionPointName + "' 在任务[" + taskName + "]下已经创建");
                }
            }


            if (!errorMessages.isEmpty()) {
                String finalMessage = String.join("\n", errorMessages);
                consumer.accept(ImportProgressEvent.builder().taskId(s).status(ImportStatus.FAILED).progress(100).message(finalMessage).build());
                return ImportResult.builder().success(false).message(finalMessage).build();
            }

            consumer.accept(ImportProgressEvent.builder().taskId(s).status(ImportStatus.PROCESSING).progress(80).message("数据分类完成，准备写入数据库...").build());

            // 批量保存和更新
            if (!pointsToInsert.isEmpty()) {
                this.saveBatch(pointsToInsert);
            }
            if (!pointsToUpdate.isEmpty()) {
                this.updateBatchById(pointsToUpdate);
            }

            long endTime = System.currentTimeMillis();
            String message = String.format("导入完成，总耗时: %.2f秒。新增 %d 条，更新 %d 条，失败 %d 条。",
                    (endTime - startTime) / 1000.0, pointsToInsert.size(), pointsToUpdate.size(), failList.size() + errorMessages.size());

            consumer.accept(ImportProgressEvent.builder().taskId(s).status(ImportStatus.SUCCESS).progress(100).message(message).build());

            return ImportResult.builder()
                    .success(true)
                    .totalRecords(successList.size() + failList.size())
                    .successRecords(pointsToInsert.size() + pointsToUpdate.size())
                    .failedRecords(failList.size() + errorMessages.size())
                    .message(message)
                    .build();
        }
        catch (Exception e)
        {
            log.error("文件处理失败", e);
            consumer.accept(ImportProgressEvent.builder().taskId(s).status(ImportStatus.FAILED).progress(100).message("文件处理失败: " + e.getMessage()).build());
            return ImportResult.builder()
                    .success(false)
                    .message("文件处理失败: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public Set<String> getSupportedFileTypes()
    {
        return CollectionUtil.newHashSet(".xlsx", ".xls");
    }

    @Override
    public String getProcessorName()
    {
        return FileProcessorConstants.FUNCTION_POINT_IMPORT;
    }

    @Override
    public Class<FunctionPointsImportTaskSaveDTO> getExtraType(){
        return FunctionPointsImportTaskSaveDTO.class;
    }


    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> set) {
        return listByIds(set).stream().collect(Collectors.toMap(TestRequirementFunctionPoints::getId, v->v));
    }

    @Override
    public boolean saveOrUpdateBatch(Collection<TestRequirementFunctionPoints> entityList) {
        if (CollectionUtil.isEmpty(entityList)) {
            return true;
        }


        List<TestRequirementFunctionPoints> insertList = entityList.stream()
                .filter(e -> e.getId() == null)
                .collect(Collectors.toList());
        List<TestRequirementFunctionPoints> updateList = entityList.stream()
                .filter(e -> e.getId() != null)
                .collect(Collectors.toList());

        if (!insertList.isEmpty()) {

            LbqWrapper<TestRequirementFunctionPoints> queryWrapper = Wraps.lbQ();
            queryWrapper.and(wrapper -> {
                for (TestRequirementFunctionPoints entity : insertList) {
                    wrapper.or(orWrapper -> {
                        orWrapper.eq(TestRequirementFunctionPoints::getFunctionPoint, entity.getFunctionPoint())
                                .eq(TestRequirementFunctionPoints::getProjectId, entity.getProjectId());
                        if (entity.getIssueTestReqId() != null) {
                            orWrapper.eq(TestRequirementFunctionPoints::getIssueTestReqId, entity.getIssueTestReqId());
                        } else {
                            orWrapper.isNull(TestRequirementFunctionPoints::getIssueTestReqId);
                        }
                    });
                }
            });

            List<TestRequirementFunctionPoints> existingPoints = this.list(queryWrapper);

            if (CollectionUtil.isNotEmpty(existingPoints)) {

                Set<Serializable> taskIds = existingPoints.stream()
                        .map(TestRequirementFunctionPoints::getTaskId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toSet());

                Map<Long, Task> taskMap = new HashMap<>();
                if (!taskIds.isEmpty()) {
                    Map<Serializable, Object> taskResults = taskApi.findByIds(taskIds);
                    if (taskResults != null) {
                        for (Map.Entry<Serializable, Object> entry : taskResults.entrySet()) {
                            taskMap.put(Long.valueOf(entry.getKey().toString()), BeanPlusUtil.toBean(entry.getValue(), Task.class));
                        }
                    }
                }


                Map<String, TestRequirementFunctionPoints> existingMap = existingPoints.stream()
                        .collect(Collectors.toMap(
                                p -> p.getProjectId() + "#" + p.getIssueTestReqId() + "#" + p.getFunctionPoint(),
                                Function.identity(),
                                (p1, p2) -> p1 // In case of duplicates in DB, just take the first one
                        ));

                List<String> errorMessages = new ArrayList<>();
                for (TestRequirementFunctionPoints newPoint : insertList) {
                    String key = newPoint.getProjectId() + "#" + newPoint.getIssueTestReqId() + "#" + newPoint.getFunctionPoint();
                    TestRequirementFunctionPoints conflictingPoint = existingMap.get(key);
                    if (conflictingPoint != null) {
                        Task task = taskMap.get(conflictingPoint.getTaskId());
                        String taskName = (task != null) ? task.getName() : "未知";
                        errorMessages.add("当前分析要点 '" + newPoint.getFunctionPoint() + "' 在任务[" + taskName + "]下已经创建");
                    }
                }

                if (!errorMessages.isEmpty()) {
                    throw new BizException(String.join("; ", errorMessages));
                }
            }

            super.saveBatch(insertList);
        }

        if (!updateList.isEmpty()) {
            super.updateBatchById(updateList);
        }

        return true;
    }

    @Override
    public boolean save(TestRequirementFunctionPoints entity) {
        LbqWrapper<TestRequirementFunctionPoints> queryWrapper = Wraps.<TestRequirementFunctionPoints>lbQ()
                .eq(TestRequirementFunctionPoints::getFunctionPoint, entity.getFunctionPoint())
                .eq(TestRequirementFunctionPoints::getProjectId, entity.getProjectId());

        if (entity.getIssueTestReqId() != null) {
            queryWrapper.eq(TestRequirementFunctionPoints::getIssueTestReqId, entity.getIssueTestReqId());
        } else {
            queryWrapper.isNull(TestRequirementFunctionPoints::getIssueTestReqId);
        }

        TestRequirementFunctionPoints existingPoint = this.getOne(queryWrapper, false);

        if (existingPoint != null) {
            Task task = taskApi.getById(existingPoint.getTaskId());
            String taskName = (task != null) ? task.getName() : "未知";
            throw new BizException("当前分析要点 '" + entity.getFunctionPoint() + "' 在任务[" + taskName + "]下已经创建");
        }

        return super.save(entity);
    }
}
