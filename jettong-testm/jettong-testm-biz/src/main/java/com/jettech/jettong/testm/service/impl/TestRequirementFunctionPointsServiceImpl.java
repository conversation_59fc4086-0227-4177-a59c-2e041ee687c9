package com.jettech.jettong.testm.service.impl;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.jettech.basic.utils.BeanPlusUtil;
import com.jettech.jettong.alm.api.TaskApi;
import com.jettech.jettong.alm.issue.entity.Task;
import com.jettech.jettong.base.api.UserApi;
import com.jettech.jettong.base.entity.rbac.user.User;
import com.jettech.basic.base.service.SuperServiceImpl;
import com.jettech.basic.database.mybatis.conditions.Wraps;
import com.jettech.basic.database.mybatis.conditions.query.LbqWrapper;
import com.jettech.basic.exception.BizException;
import com.jettech.basic.fileimport.model.ImportProgressEvent;
import com.jettech.basic.fileimport.model.ImportResult;
import com.jettech.basic.fileimport.model.ImportStatus;
import com.jettech.jettong.alm.api.ProjectApi;
import com.jettech.jettong.alm.api.TestreqApi;
import com.jettech.jettong.alm.issue.entity.Testreq;
import com.jettech.jettong.alm.project.entity.ProjectInfo;
import com.jettech.jettong.common.constant.FileProcessorConstants;
import com.jettech.jettong.common.util.poi.ExcelDownLoadUtil;
import com.jettech.jettong.common.util.poi.ExcelExportPlusUtil;
import com.jettech.jettong.product.api.ProductInfoApi;
import com.jettech.jettong.product.api.ProductModuleFunctionApi;
import com.jettech.jettong.product.entity.ProductInfo;
import com.jettech.jettong.product.entity.ProductModuleFunction;
import com.jettech.jettong.testm.config.HierarchyConfig;
import com.jettech.jettong.testm.dao.TestRequirementFunctionPointsMapper;
import com.jettech.jettong.testm.dto.FunctionPointsImportTaskSaveDTO;
import com.jettech.jettong.testm.entity.TestRequirementAnalysisConfig;
import com.jettech.jettong.testm.entity.TestRequirementFunctionPoints;
import com.jettech.jettong.testm.dto.TestRequirementFunctionPointsPageQuery;
import com.jettech.jettong.testm.poi.TestRequirementFunctionPointDicHandlerImpl;
import com.jettech.jettong.testm.poi.TestRequirementFunctionPointVerifyHandler;
import com.jettech.jettong.testm.service.TestRequirementAnalysisConfigService;
import com.jettech.jettong.testm.service.TestRequirementFunctionPointsService;
import com.jettech.jettong.testm.vo.PointsDataVo;
import com.jettech.jettong.testm.vo.TestPointsTreeVO;
import com.jettech.jettong.testm.vo.TestPointsTreeNodeVO;

import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.Serializable;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.poi.ss.usermodel.Font.COLOR_RED;

@Slf4j
@Service
@DS("#thread.tenant")
public class TestRequirementFunctionPointsServiceImpl
        extends SuperServiceImpl<TestRequirementFunctionPointsMapper, TestRequirementFunctionPoints>
        implements TestRequirementFunctionPointsService
{
    private final ProductInfoApi productInfoApi;
    private final TestRequirementAnalysisConfigService configService;
    private final TestreqApi testreqApi;
    private final ProjectApi projectApi;
    private final ProductModuleFunctionApi productModuleFunctionApi;
    private final UserApi userApi;
    private final TaskApi taskApi;

    public TestRequirementFunctionPointsServiceImpl(ProductInfoApi productInfoApi, TestRequirementAnalysisConfigService configService, TestreqApi testreqApi, ProjectApi projectApi, ProductModuleFunctionApi productModuleFunctionApi,
            UserApi userApi, TaskApi taskApi) {
        this.productInfoApi = productInfoApi;
        this.configService = configService;
        this.testreqApi = testreqApi;
        this.projectApi = projectApi;
        this.productModuleFunctionApi = productModuleFunctionApi;
        this.userApi = userApi;
        this.taskApi = taskApi;
    }

    public static final String SHEETNAME = "测试分析";

    @Override
    public List<Long> getFunctionIdsByProjectAndModule(Long projectId, Long productModuleFunctionId)
    {
        List<Long> byProjectId = productInfoApi.findByProjectId(projectId, productModuleFunctionId);
        return byProjectId;
    }

    @Override
    public List<Long> getFunctionIdsByFunctionId(Long productModuleFunctionId)
    {
        List<Long> byProjectId = productInfoApi.findByFunctionId(productModuleFunctionId);
        return byProjectId;
    }

    @Override
    public void addCaseCount(Long pointId)
    {
        TestRequirementFunctionPoints byId = this.getById(pointId);
        byId.setCaseCount(byId.getCaseCount() + 1);
        this.updateById(byId);
    }

    @Override
    public void redCaseCount(Long pointId)
    {
        TestRequirementFunctionPoints byId = this.getById(pointId);
        byId.setCaseCount(byId.getCaseCount() - 1);
        this.updateById(byId);
    }

    /**
     * 生成测试要点脑图树形结构
     *
     * @param projectId 项目ID，不能为空
     * @param reqId 需求ID，可以为空
     * @return TestPointsTreeVO 脑图对象
     * <AUTHOR>
     * @description 根据项目ID和需求ID生成脑图
     * @date 2025-08-25
     */
    @Override
    public TestPointsTreeVO generateTestPointsTree(Long projectId, Long reqId, Long taskId)
    {
        TestPointsTreeVO result = new TestPointsTreeVO();

        TestRequirementAnalysisConfig config = getEnabledConfig();
        if (config == null || config.getHierarchyConfig() == null || config.getFieldConfig() == null ||
                config.getTestpointParsingConfig() == null)
        {
            throw new BizException("未找到有效的测试需求分析配置");
        }

        HierarchyConfig hierarchyConfig = JSON.parseObject(config.getHierarchyConfig(), HierarchyConfig.class);

        TestPointsTreeNodeVO rootNode = buildTreeStructure(projectId, reqId, taskId, hierarchyConfig);
        if (rootNode == null)
        {
            return null;
        }

        String layout = hierarchyConfig.getLayout() != null ? hierarchyConfig.getLayout() : "logicalStructure";
        result.setLayout(layout);

        result.setRoot(rootNode);

        JSONObject theme = buildThemeFromConfig(hierarchyConfig);
        result.setTheme(theme);

        return result;
    }

    /**
     * 构建简单的树形结构
     *
     * @param projectId 项目ID
     * @param reqId 任务 ID
     * @param hierarchyConfig 层级配置
     * @return TestPointsTreeNodeVO 树形根节点
     * <AUTHOR>
     * @description 根据配置构建树结构
     * @date 2025-08-25
     */
    private TestPointsTreeNodeVO buildTreeStructure(Long projectId, Long reqId, Long taskId,
            HierarchyConfig hierarchyConfig)
    {

        LbqWrapper<TestRequirementFunctionPoints> wrapper =
                Wraps.<TestRequirementFunctionPoints>lbQ().eq(TestRequirementFunctionPoints::getProjectId, projectId);
        if (taskId != null)
        {
            wrapper.eq(TestRequirementFunctionPoints::getTaskId, taskId);
        }
        if (reqId != null)
        {
            wrapper.eq(TestRequirementFunctionPoints::getIssueTestReqId, reqId);
        }

        List<TestRequirementFunctionPoints> functionPoints = list(wrapper);

        List<Long> functionIds = functionPoints.stream().map(TestRequirementFunctionPoints::getFunctionId).distinct()
                .collect(Collectors.toList());

        List<ProductModuleFunction> functions = productModuleFunctionApi.getProductModuleFunctionListByIds(functionIds);

        if (functions.isEmpty())
        {
            return null;
        }

        List<Long> productIds =
                functions.stream().map(ProductModuleFunction::getProductId).distinct().collect(Collectors.toList());
        List<ProductInfo> productInfos = productInfoApi.selectProductInfoByIds(productIds);

        Map<Long, String> productMap =
                productInfos.stream().collect(Collectors.toMap(ProductInfo::getId, ProductInfo::getName));

        List<HierarchyConfig.LevelConfig> enabledLevels =
                hierarchyConfig.getHierarchy().stream().filter(HierarchyConfig.LevelConfig::getEnabled)
                        .sorted(Comparator.comparingInt(HierarchyConfig.LevelConfig::getLevel))
                        .collect(Collectors.toList());

        TestPointsTreeNodeVO rootNode = createRootTreeNodeFromConfig(projectId, reqId, hierarchyConfig);

        long nodeId = 1L;

        Map<Long, List<ProductModuleFunction>> functionsByProduct =
                functions.stream().collect(Collectors.groupingBy(ProductModuleFunction::getProductId));

        for (Map.Entry<Long, List<ProductModuleFunction>> entry : functionsByProduct.entrySet())
        {
            Long productId = entry.getKey();
            List<ProductModuleFunction> productFunctions = entry.getValue();
            HierarchyConfig.LevelConfig systemConfig = getLevelConfigByLevelAndType(enabledLevels, 2, "system");
            String systemName = productMap.get(productId);
            TestPointsTreeNodeVO systemNode =
                    createTreeNodeVO(systemName, systemConfig.getType(), systemConfig.getTypeName(), nodeId++, rootNode.getId(), systemConfig.getStyle());

            HierarchyConfig.LevelConfig tradeConfig = getLevelConfigByLevelAndType(enabledLevels, 3, "trade");
            List<ProductModuleFunction> trades =
                    productFunctions.stream().filter(f -> f.getNodeType() != null && f.getNodeType() == 2)
                            .collect(Collectors.toList());

            for (ProductModuleFunction trade : trades)
            {
                TestPointsTreeNodeVO tradeNode =
                        createTreeNodeVO(trade.getName(), tradeConfig.getType(), tradeConfig.getTypeName(), nodeId++, systemNode.getId(), tradeConfig.getStyle());

                List<TestRequirementFunctionPoints> tradeFunctionPoints =
                        functionPoints.stream().filter(point -> trade.getId().equals(point.getFunctionId()))
                                .collect(Collectors.toList());

                HierarchyConfig.LevelConfig functionPointConfig =
                        getLevelConfigByLevelAndType(enabledLevels, 4, "functionPoint");
                for (TestRequirementFunctionPoints functionPoint : tradeFunctionPoints)
                {
                    TestPointsTreeNodeVO testPointNode =
                            createTreeNodeVO(functionPoint.getFunctionPoint(), functionPointConfig.getType(), functionPointConfig.getTypeName(), nodeId++, tradeNode.getId(),
                                    functionPointConfig.getStyle());

                    nodeId = buildAttributeNodesForTestPoint(testPointNode, functionPoint, enabledLevels, nodeId);

                    tradeNode.getChildren().add(testPointNode);
                }

                if (!tradeNode.getChildren().isEmpty())
                {
                    systemNode.getChildren().add(tradeNode);
                }
            }

            if (!systemNode.getChildren().isEmpty())
            {
                rootNode.getChildren().add(systemNode);
            }
        }

        return rootNode;
    }

    /**
     * 根据配置创建树形根节点
     *
     * @param projectId 项目ID
     * @param reqId 需求ID
     * @param hierarchyConfig 层级配置
     * @return 根节点
     * <AUTHOR>
     * @date 2025-08-25
     */
    private TestPointsTreeNodeVO createRootTreeNodeFromConfig(Long projectId, Long reqId,
            HierarchyConfig hierarchyConfig)
    {
        HierarchyConfig.LevelConfig rootConfig = getLevelConfigByLevelAndType(hierarchyConfig.getHierarchy(), 1, null);

        if (rootConfig == null)
        {
            throw new BizException("测试需求分析配置有误");
        }

        String rootName = buildNodeNameFromTemplate(rootConfig.getNameTemplate(), projectId, reqId, null, null, null);
        return createTreeNodeVO(rootName, rootConfig.getType(), rootConfig.getTypeName(), 0L, null, rootConfig.getStyle());

    }

    /**
     * 创建节点
     *
     * @param text 节点名称
     * @param id 节点ID
     * @param parentId 父节点ID
     * @return TestPointsTreeNodeVO 节点
     */
    private TestPointsTreeNodeVO createTreeNodeVO(String text, String type, String typeName, Long id, Long parentId, JSONObject style)
    {
        TestPointsTreeNodeVO node = new TestPointsTreeNodeVO();
        node.setId(id);
        node.setParentId(parentId);
        node.setName(text);
        node.setSort(0);

        PointsDataVo dataVo = new PointsDataVo();
        dataVo.setText(text);
        dataVo.setType(type);
        dataVo.setTypeName(typeName);
        node.setData(dataVo);

        if (style != null)
        {
            dataVo.setFillColor(style.getString("fillColor"));
            dataVo.setBorderColor(style.getString("borderColor"));
            dataVo.setBorderRadius(style.getInteger("borderRadius"));
            dataVo.setBorderWidth(style.getInteger("borderWidth"));
            dataVo.setColor(style.getString("color"));
            dataVo.setLineColor(style.getString("lineColor"));
        }

        node.initChildren();
        return node;
    }

    /**
     * 为测试点构建属性节点
     *
     * @param testPointNode 测试点节点
     * @param testPoint 测试点数据
     * @param enabledLevels 启用的层级配置列表
     * @param nodeId 当前节点ID
     * @return long 更新后的节点ID
     * <AUTHOR>
     * @description 根据配置为测试点构建属性层级节点，对测试要素分割以及多选字段等特殊处理
     * @date 2025-08-25
     */
    private long buildAttributeNodesForTestPoint(TestPointsTreeNodeVO testPointNode,
            TestRequirementFunctionPoints testPoint, List<HierarchyConfig.LevelConfig> enabledLevels, long nodeId)
    {

        List<HierarchyConfig.LevelConfig> attributeConfigs =
                enabledLevels.stream().filter(level -> "attribute".equals(level.getType()))
                        .sorted(Comparator.comparingInt(HierarchyConfig.LevelConfig::getSort))
                        .collect(Collectors.toList());

        TestRequirementAnalysisConfig config = getEnabledConfig();

        for (HierarchyConfig.LevelConfig attributeConfig : attributeConfigs)
        {
            String typeName = attributeConfig.getTypeName();
            String type = attributeConfig.getType();

            TestPointsTreeNodeVO attributeNode =
                    createTreeNodeVO(typeName, type, typeName, nodeId++, testPointNode.getId(), attributeConfig.getStyle());

            Object attributeValue = getAttributeValue(testPoint, attributeConfig.getDataField(), config);

            nodeId = addAttributeValueNodes(attributeNode, attributeValue, attributeConfig.getDataField(), testPoint, config, nodeId);

            testPointNode.getChildren().add(attributeNode);
        }

        return nodeId;
    }

    /**
     * 添加属性值节点
     *
     * @param attributeNode 属性节点
     * @param attributeValue 属性值（可能是String或List<String>）
     * @param dataField 数据字段名
     * @param testPoint 测试点数据
     * @param config 配置
     * @param nodeId 当前节点ID
     * @return long 更新后的节点ID
     */
    private long addAttributeValueNodes(TestPointsTreeNodeVO attributeNode, Object attributeValue, String dataField, 
            TestRequirementFunctionPoints testPoint, TestRequirementAnalysisConfig config, long nodeId)
    {

        if ("testPoints".equals(dataField))
        {
            List<String> testElements = parseTestPoints(testPoint.getTestPoints(), config);
            if (!testElements.isEmpty())
            {
                for (String element : testElements)
                {
                    TestPointsTreeNodeVO elementNode = createTreeNodeVO(element, null, null, nodeId++, attributeNode.getId(), null);
                    attributeNode.getChildren().add(elementNode);
                }
            }
            else
            {
                TestPointsTreeNodeVO emptyNode = createTreeNodeVO("未设置", null, null, nodeId++, attributeNode.getId(), null);
                attributeNode.getChildren().add(emptyNode);
            }
        }
        else if (attributeValue instanceof JSONArray)
        {
            JSONArray options = (JSONArray) attributeValue;

            for (int i = 0; i < options.size(); i++)
            {
                JSONObject option = options.getJSONObject(i);
                String text = option.getString("text");
                JSONObject style = option.getJSONObject("style");

                TestPointsTreeNodeVO valueNode = createTreeNodeVO(
                        text,
                        null, null, 
                        nodeId++,
                        attributeNode.getId(),
                        style
                );
                attributeNode.getChildren().add(valueNode);
            }
        }
        else if (attributeValue instanceof JSONObject)
        {
            JSONObject option = (JSONObject) attributeValue;
            String text = option.getString("text");
            JSONObject style = option.getJSONObject("style");

            TestPointsTreeNodeVO valueNode = createTreeNodeVO(
                    text,
                    null, null, 
                    nodeId++,
                    attributeNode.getId(),
                    style
            );
            attributeNode.getChildren().add(valueNode);
        }
        else
        {
            String value = attributeValue != null ? attributeValue.toString() : "未设置";
            TestPointsTreeNodeVO valueNode = createTreeNodeVO(value, null, null, nodeId++, attributeNode.getId(), null);
            attributeNode.getChildren().add(valueNode);
        }

        return nodeId;
    }

    /**
     * 根据数据字段获取属性值
     *
     * @param point 测试要点数据对象
     * @param dataField 数据字段名称
     * @param config 测试需求分析配置
     * @return String 属性值的字符串表示，未设置时返回"未设置"
     * <AUTHOR>
     * @description 根据数据字段名称从测试要点对象中获取对应的属性值，
     * @date 2025-08-25
     */
    private Object getAttributeValue(TestRequirementFunctionPoints point, String dataField,
            TestRequirementAnalysisConfig config)
    {
        try
        {
            JSONArray fieldConfigs = JSON.parseArray(config.getFieldConfig());
            JSONObject fieldConfig = findFieldConfig(fieldConfigs, dataField);

            if (fieldConfig == null)
            {
                throw new BizException("测试需求分析配置有误，未找到字段配置");
            }

            Object rawValue = getFieldValue(point, dataField);

            return parseValueByFieldConfig(rawValue, fieldConfig);

        }
        catch (Exception e)
        {
            log.warn("解析字段值失败，dataField: {}, error: {}", dataField, e.getMessage());
            return getFieldValue(point, dataField);
        }
    }

    /**
     * 查找指定字段的配置
     *
     * @param fieldConfigs 字段配置数组
     * @param dataField 字段名
     * @return JSONObject 字段配置对象
     */
    private JSONObject findFieldConfig(JSONArray fieldConfigs, String dataField)
    {
        for (int i = 0; i < fieldConfigs.size(); i++)
        {
            JSONObject fieldConfig = fieldConfigs.getJSONObject(i);
            if (dataField.equals(fieldConfig.getString("key")))
            {
                return fieldConfig;
            }
        }
        return null;
    }

    /**
     * 获取字段的原始值
     *
     * @param point 测试要点对象
     * @param dataField 字段名
     * @return Object 字段值
     */
    private Object getFieldValue(TestRequirementFunctionPoints point, String dataField)
    {
        switch (dataField)
        {
            case "functionPoint":
                return point.getFunctionPoint();
            case "priority":
                return point.getPriority();
            case "ruleType":
                return point.getRuleType();
            case "involveAccount":
                return point.getInvolveAccount();
            case "involveBatch":
                return point.getInvolveBatch();
            case "testPoints":
                return point.getTestPoints();
            case "ruleSource":
                return point.getRuleSource();
            case "caseCount":
                return point.getCaseCount() == null ? 0 : point.getCaseCount();
            case "remark":
                return point.getRemark();
            case "c1":
                return point.getC1();
            case "c2":
                return point.getC2();
            case "c3":
                return point.getC3();
            case "c4":
                return point.getC4();
            case "c5":
                return point.getC5();
            default:
                return null;
        }
    }

    /**
     * 根据字段配置解析值
     *
     * @param rawValue 原始值
     * @param fieldConfig 字段配置
     * @return Object 解析后的值，可能是String或List<String>
     */
    private Object parseValueByFieldConfig(Object rawValue, JSONObject fieldConfig)
    {
        if (rawValue == null)
        {
            return "";
        }

        String fieldType = fieldConfig.getString("field_type");

        switch (fieldType)
        {
            case "SELECT":
                return parseSelectOption(rawValue, fieldConfig);
            case "MULTI-SELECT":
                return parseMultiSelectOptions(rawValue, fieldConfig);
            case "TEXT":
            case "TEXTAREA":
                return rawValue.toString();
            default:
                return rawValue.toString();
        }
    }

    /**
     * 解析单选类型字段的选项
     *
     * @param rawValue 原始值
     * @param fieldConfig 字段配置
     * @return JSONObject 匹配的选项对象，包含 text、value、style 等信息
     */
    private JSONObject parseSelectOption(Object rawValue, JSONObject fieldConfig)
    {
        JSONArray options = fieldConfig.getJSONArray("options");
        if (options == null || options.isEmpty())
        {
            JSONObject fallbackOption = new JSONObject();
            fallbackOption.put("text", rawValue.toString());
            fallbackOption.put("value", rawValue);
            return fallbackOption;
        }

        for (int i = 0; i < options.size(); i++)
        {
            JSONObject option = options.getJSONObject(i);
            Object optionValue = option.get("value");

            if (isValueMatch(rawValue, optionValue))
            {
                return option;
            }
        }

        JSONObject fallbackOption = new JSONObject();
        fallbackOption.put("text", rawValue.toString());
        fallbackOption.put("value", rawValue);
        return fallbackOption;
    }

    /**
     * 解析多选类型字段的选项
     *
     * @param rawValue 原始值（逗号分隔的字符串）
     * @param fieldConfig 字段配置
     * @return JSONArray 匹配的选项对象数组，每个元素包含 text、value、style 等信息
     */
    private JSONArray parseMultiSelectOptions(Object rawValue, JSONObject fieldConfig)
    {
        JSONArray result = new JSONArray();

        if (rawValue == null || rawValue.toString().trim().isEmpty())
        {
            JSONObject fallbackOption = new JSONObject();
            fallbackOption.put("text", "未设置");
            fallbackOption.put("value", "");
            result.add(fallbackOption);
            return result;
        }

        JSONArray options = fieldConfig.getJSONArray("options");
        if (options == null || options.isEmpty())
        {
            JSONObject fallbackOption = new JSONObject();
            fallbackOption.put("text", rawValue.toString());
            fallbackOption.put("value", rawValue);
            result.add(fallbackOption);
            return result;
        }

        String[] values = rawValue.toString().split(",");

        for (String value : values)
        {
            String trimmedValue = value.trim();
            if (trimmedValue.isEmpty())
            {
                continue;
            }

            boolean found = false;
            for (int i = 0; i < options.size(); i++)
            {
                JSONObject option = options.getJSONObject(i);
                Object optionValue = option.get("value");

                if (isValueMatch(trimmedValue, optionValue))
                {
                    result.add(option);
                    found = true;
                    break;
                }
            }

            if (!found)
            {
                JSONObject fallbackOption = new JSONObject();
                fallbackOption.put("text", trimmedValue);
                fallbackOption.put("value", trimmedValue);
                result.add(fallbackOption);
            }
        }

        if (result.isEmpty())
        {
            JSONObject fallbackOption = new JSONObject();
            fallbackOption.put("text", "未设置");
            fallbackOption.put("value", "");
            result.add(fallbackOption);
        }

        return result;
    }

    /**
     * 判断值是否匹配
     *
     * @param rawValue 原始值
     * @param optionValue 选项值
     * @return boolean 是否匹配
     */
    private boolean isValueMatch(Object rawValue, Object optionValue)
    {
        if (rawValue == null && optionValue == null)
        {
            return true;
        }
        if (rawValue == null || optionValue == null)
        {
            return false;
        }

        if (rawValue instanceof String && optionValue instanceof String)
        {
            return rawValue.equals(optionValue);
        }

        if (rawValue instanceof Boolean && optionValue instanceof Boolean)
        {
            return rawValue.equals(optionValue);
        }

        if (rawValue instanceof Number && optionValue instanceof Number)
        {
            return rawValue.toString().equals(optionValue.toString());
        }

        return rawValue.toString().equals(optionValue.toString());
    }

    // ... (rest of the file)
